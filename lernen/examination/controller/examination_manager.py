import time
import json
from core.controller.utils.cache_provider import *
from core.controller.utils.restclient import *
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *

cache_provider = get_cache_provider()
restclientWithoutUser = getrestclientWithoutUser();

EXAMINATION_PREFERENCES_CACHE_NAMESPACE = "examination_preferences"

def get_exam_forest(user_login_view, institute_unique_code, standard_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/standard/"+str(standard_id)+"/forest?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_exam_courses(user_login_view, institute_unique_code, standard_id, academic_session_id, only_leaf_exams=False, is_filtered_dimension=False):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/standard/"+str(standard_id)+"/assigned-courses?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&only_leaf_exams="+str(only_leaf_exams)+"&is_filtered_dimension="+str(is_filtered_dimension)).get_data()

def get_exam_details(user_login_view, institute_unique_code, exam_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/"+str(exam_id)+"?institute_id="+str(institute_id)).get_data()

def get_dimensions(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/dimension/institute/"+str(institute_id)).get_data()

def create_exam(user_login_view, institute_unique_code, exam_creation_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/examination/create?institute_id="+str(institute_id)+"&user_id=" + user['uuid'], exam_creation_payload)
    if not response.is_success():
        return {'success' : False , 'parentExamId' : exam_creation_payload['parentExamId'], 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Exam Created successfully!!!", 'parentExamId' : exam_creation_payload['parentExamId'],'newExamId' : response.get_data()['examId']}

def add_dimension(user_login_view, institute_unique_code, dimension_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    dimension_data['dimensionType'] = 'CUSTOM'
    response = restclient.post(user_login_view, "/2.0/examination/dimension?institute_id="+str(institute_id),dimension_data)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Dimension Created successfully!!!"}

def update_dimension(user_login_view, institute_unique_code, dimension_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    dimension_data['dimensionType'] = 'CUSTOM'
    response = restclient.put(user_login_view, "/2.0/examination/dimension?institute_id="+str(institute_id),dimension_data)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Dimension - "+dimension_data['dimensionName']+" Updated Successfully!!!"}

def delete_dimension(user_login_view, institute_unique_code, dimension_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.delete(user_login_view, "/2.0/examination/dimension/"+str(dimension_id)+"?institute_id="+str(institute_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Dimension deleted Successfully!!!"}

def get_datesheet(user_login_view, institute_unique_code, academic_session_id, standard_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/datesheet-syllabus/"+str(institute_id)+"?academic_session_id=" + str(academic_session_id)+"&standard_id="+str(standard_id)).get_data()

def get_datesheet_detail(user_login_view, institute_unique_code, academic_session_id, standard_id, datesheet_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/datesheet-details/"+str(institute_id)+"?academic_session_id=" + str(academic_session_id)+"&standard_id="+str(standard_id)+"&datesheet_id=" + str(datesheet_id)).get_data()

def add_datesheet(user_login_view, institute_unique_code, datesheet_and_syllabus_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    datesheet_and_syllabus_payload['instituteId'] = institute_id
    response = restclient.post(user_login_view, "/2.0/examination/datesheet-syllabus?institute_id="+str(institute_id)+"&user_id=" + user['uuid'], datesheet_and_syllabus_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Datesheet Added Successfully!!!"}

def update_datesheet(user_login_view, institute_unique_code, datesheet_and_syllabus_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    datesheet_and_syllabus_payload['instituteId'] = institute_id
    response = restclient.put(user_login_view, "/2.0/examination/datesheet-syllabus?institute_id="+str(institute_id)+"&user_id=" + user['uuid'], datesheet_and_syllabus_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Datesheet Updated Successfully!!!"}

def delete_datesheet(user_login_view, institute_unique_code, datesheet_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/examination/datesheet-syllabus?institute_id="+str(institute_id)+"&user_id=" + user['uuid']+"&datesheet_id="+ str(datesheet_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Datesheet Deleted Successfully!!!"}

def delete_exam(user_login_view, institute_unique_code, exam_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/examination/delete/"+str(exam_id)+"?institute_id="+str(institute_id)+"&user_id=" + user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Exam deleted Successfully!!!"}

def get_exam_grades(user_login_view, institute_unique_code, exam_id, course_type):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/grades/exam/"+str(exam_id)+"?institute_id="+str(institute_id)+"&course_type="+str(course_type)).get_data()

def get_class_marks_feed_structure(user_login_view, institute_unique_code, standard_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/standard/"+str(standard_id)+"/marks-feed-structure?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_class_marks_feed_structure_filter(user_login_view, institute_unique_code, standard_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/standard/"+str(standard_id)+"/marks-feed-structure-dimensions/filter?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_class_marks_by_course(user_login_view, institute_unique_code, academic_session_id, exam_id, course_id, standard_id, section_id, add_relieved_students, dimension_id_str, course_type):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/examination/standard/"+str(standard_id)+"/exam/"+str(exam_id)+"/course/"+str(course_id)+"/dimension-with-marks?institute_id="+str(institute_id)+"&section_id="+str(section_id)+"&add_relieved_students="+str(add_relieved_students)+"&user_id="+user['uuid']+"&academic_session_id="+str(academic_session_id)+"&dimension_id_str="+str(dimension_id_str)+"&course_type="+str(course_type)).get_data()

def get_resolved_class_marks_by_course(user_login_view, institute_unique_code, academic_session_id, exam_id, course_id, standard_id, section_id, add_relieved_students, filter_based_on_login_user_course_assignment):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user_id = ""
    if filter_based_on_login_user_course_assignment:
        user = user_login_view['user']
        user_id = user['uuid']
    return restclient.get(user_login_view, "/2.0/examination/"+str(exam_id)+"/course/"+str(course_id)+"/effective-marks?institute_id="+str(institute_id)+"&section_id="+str(section_id)+"&add_relieved_students="+str(add_relieved_students)+"&user_id="+user_id+"&academic_session_id="+str(academic_session_id)+"&standard_id="+str(standard_id)).get_data()


def submit_marks(user_login_view, institute_unique_code, marks_feed_data, status, section_id, academic_session_id, standard_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/examination/submit-marks?institute_id="+str(institute_id)+"&status="+status+"&section_id="+str(section_id)+"&user_id="+user['uuid']+"&academic_session_id="+str(academic_session_id)+"&standard_id="+str(standard_id),marks_feed_data)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    if(status == "SAVED"):
        return {'success' : True , 'message' : "Marks saved successfully!!!"}
    else:
        return {'success' : True , 'message' : "Marks submitted successfully!!!"}

def update_exam_dimension(user_login_view, institute_unique_code, exam_id, update_exam_dimension_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/examination/update-exam-dimension/"+exam_id+"?institute_id="+str(institute_id)+"&user_id=" + user['uuid'],update_exam_dimension_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Exam Dimensions updated successfully"}

def update_exam_metadata(user_login_view, institute_unique_code, exam_id, update_exam_metadata_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    update_exam_metadata_payload['examId'] = exam_id
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/examination/update-exam-metadata?institute_id="+str(institute_id)+"&user_id=" + user['uuid'], update_exam_metadata_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Exam updated successfully"}


def update_exam_courses(user_login_view, institute_unique_code, exam_id, course_type, update_exam_courses_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/examination/"+exam_id+"/assign-courses/"+course_type+"?institute_id="+str(institute_id)+"&user_id=" + user['uuid'],update_exam_courses_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Exam Courses updated successfully"}


def get_institute_stats(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/"+str(institute_id)+"/top-stats?academic_session_id="+str(academic_session_id)).get_data()

def get_student_report_card(user_login_view, institute_unique_code, academic_session_id, student_id, report_type):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/examination/marks-report/"+str(student_id)+"/"+str(report_type)+"/pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&compute_rank=true"+"&user_id="+user['uuid'])

def get_class_report_card(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, report_type, student_per_page):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    if section_id is None:
        section_id = ""
    if student_per_page is None:
        student_per_page = ""
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/examination/marks-report/standard/"+str(standard_id)+"/"+str(report_type)+"/pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&section_id="+str(section_id)+"&user_id="+user['uuid']+"&student_per_page="+str(student_per_page))

def get_bulk_student_report_card(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, report_type,student_ids):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    student_ids = student_ids.split(",")
    return restclient.get_file_with_payload_error_reason(user_login_view, "/2.0/examination/bulk-marks-report/standard/"+str(standard_id)+"/"+str(report_type)+"/pdf?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&section_id="+str(section_id)+"&user_id="+user['uuid'],student_ids)

def get_report_card_types(user_login_view, institute_unique_code, academic_session_id, standard_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/reportcard-types/"+str(standard_id)+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_all_report_card_types(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/report-card-types-name?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_report_card_variables(user_login_view, institute_unique_code, standard_id, session_id, section_id, report_type, student_id=None):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    if student_id:
        return restclient.get(user_login_view, "/2.0/examination/standard/"+str(standard_id)+"/report-card-variables?institute_id="+str(institute_id)+"&section_id="+str(section_id)+"&academic_session_id="+str(session_id)+"&report_type="+str(report_type)+"&student_id="+str(student_id)).get_data()
    return restclient.get(user_login_view, "/2.0/examination/standard/"+str(standard_id)+"/report-card-variables?institute_id="+str(institute_id)+"&section_id="+str(section_id)+"&academic_session_id="+str(session_id)+"&report_type="+str(report_type)).get_data()

def submit_report_card_variables(user_login_view, institute_unique_code, report_card_variable_details, session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    report_card_variable_details['instituteId'] = institute_id
    response = restclient.post(user_login_view, "/2.0/examination/report-card-attributes?institute_id="+str(institute_id)+"&academic_session_id="+str(session_id)+"&user_id="+user['uuid'],report_card_variable_details)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    else:
        return {'success' : True , 'message' : "Data updated successfully!!!"}

def get_session_exams_class_wise(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/exam-metadata/session-standard-map?institute_id="+str(institute_id)).get_data()

def get_class_green_sheet(user_login_view, institute_unique_code, session_id, standard_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/examination/generate-greensheet/"+str(session_id)+ "/" + str(standard_id) + "/" + str(institute_id))

def get_class_feed_marks_sheet(user_login_view, institute_unique_code, academic_session_id, exam_id, standard_id, section_id, student_count_per_page, add_relieved_students, course_id, student_sorting_parameters):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file(user_login_view, "/2.0/examination-reports/marks-feed-document/"+str(exam_id)+"/pdf?institute_id="+str(institute_id)+"&section_id="+str(section_id)+"&single_column_student_count="+str(student_count_per_page)+"&add_relieved_students="+str(add_relieved_students)+"&user_id="+user['uuid']+"&academic_session_id="+str(academic_session_id)+"&standard_id="+str(standard_id)+"&course_id="+str(course_id)+"&student_sorting_parameters="+str(student_sorting_parameters))

def get_class_exam_admitcards(user_login_view, institute_unique_code, exam_id, admit_card_type, student_ids, count_per_page):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    if student_ids:
        student_ids = student_ids.split(",")
    else:
        student_ids = []
    return restclient.get_file_with_payload(user_login_view, "/2.0/examination/admit-card/exam/"+str(exam_id)+"/pdf/" + str(admit_card_type) + "?institute_id="+str(institute_id)+ "&count_per_page="+str(count_per_page), student_ids)

def get_examination_preferences(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    cached_examination_preferences = cache_provider.get_value(EXAMINATION_PREFERENCES_CACHE_NAMESPACE, institute_id)
    if cached_examination_preferences is None:
        response = restclient.get(user_login_view, "/2.0/user-preferences/examination?institute_id="+str(institute_id)).get_data()
        cache_provider.set_value(EXAMINATION_PREFERENCES_CACHE_NAMESPACE, institute_id, response)
        return response
    return cached_examination_preferences

def get_student_exam_report_card(institute_unique_code, admission_number, student_name, father_name, report_card_type, academic_session_id, google_auth_key):
    return restclientWithoutUser.get_file_with_error_reason_without_user("/2.0/examination/result-declaration/exam-report-card?institute_unique_code=" + str(institute_unique_code) + "&admission_number=" + str(admission_number) + "&student_name="+str(student_name) + "&father_name="+str(father_name) + "&report_card_type="+str(report_card_type) + "&academic_session_id="+str(academic_session_id) + "&google_auth_key="+str(google_auth_key))

def send_exam_notifications(user_login_view, institute_unique_code, send_to_absentees, exam_id, course_id, section_id, send_notifications_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    send_notifications_payload['instituteId'] = institute_id
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/examination/bulk-sms-send/"+exam_id+"/"+course_id+"?user_id="+user['uuid']+"&send_to_absentees="+str(send_to_absentees)+"&section_id="+str(section_id)+"&institute_id="+str(institute_id),send_notifications_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Notifications request submitted successfully. Notifications are being sent. Please check history tab for status."}

def get_batch_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/examination/batch-notification-history/"+delivery_mode+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&offset="+str(offset)+"&limit="+str(limit)).get_data()

def get_individual_notification_history(user_login_view, institute_unique_code, academic_session_id, delivery_mode, offset, limit):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/examination/individual-notification-history/"+delivery_mode+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&offset="+str(offset)+"&limit="+str(limit)).get_data()

def get_batch_notification_details(user_login_view, institute_unique_code, user_type, batch_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/examination/batch-notification-detail/"+user_type+"/"+batch_id+"?institute_id="+str(institute_id)).get_data()

def get_class_exam_details(user_login_view, institute_unique_code, standard_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get(user_login_view, "/2.0/examination/exam-metadata/"+str(standard_id)+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def get_class_marks_by_course_list(user_login_view, institute_unique_code, academic_session_id, exam_id, course_id, standard_id, section_id, add_relieved_students):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/examination/"+str(exam_id)+"/marks?institute_id="+str(institute_id)+"&course_id="+str(course_id)+"&section_id="+str(section_id)+"&add_relieved_students="+str(add_relieved_students)+"&user_id="+user['uuid']+"&academic_session_id="+str(academic_session_id)+"&standard_id="+str(standard_id)).get_data()

def get_datesheet_pdf(user_login_view, institute_unique_code, datesheet_id, standard_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/examination/"+standard_id+"/pdf/" + datesheet_id + "?institute_id="+str(institute_id) + "&academic_session_id="+str(academic_session_id))

def update_exam_status(user_login_view, institute_unique_code, exam_id, status):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/examination/"+str(exam_id)+"/exam-status/"+str(status)+"?institute_id="+str(institute_id)+"&user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Exam Status Updated Successfully!!"}

def get_class_student_list(user_login_view, institute_unique_code, academic_session_id, standard_id, sectionIds):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/student/students-list/" + standard_id + "?institute_id="+str(institute_id)+ "&academic_session_id="+str(academic_session_id)+ "&section_ids="+str(sectionIds))
    if not response.is_success():
        return None
    return response.get_data()

def courses_marks_status_details(user_login_view, institute_unique_code, academic_session_id, exam_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/examination/"+str(exam_id)+"/marks-status?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+user['uuid']).get_data()

def update_course_exam_status(user_login_view, institute_unique_code, dimension_id, course_id, exam_id, status, academic_session_id, standard_id, section_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/examination/exam-marks-feed-status/"+str(status)+"?institute_id="+str(institute_id)+"&user_id="+user['uuid']+"&dimension_id="+str(dimension_id)+"&course_id="+str(course_id)+"&exam_id="+str(exam_id)+"&academic_session_id="+str(academic_session_id)+"&section_id="+str(section_id)+"&standard_id="+str(standard_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Courses Exam Status Updated Successfully!!"}

def get_class_exam_report_data(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.get(user_login_view, "/2.0/examination-reports/"+str(institute_id)+"/view-report/exam/"+report_data['exam_id']+"?section_id_str="+str(report_data['section_id_str'])+"&requiredHeaders="+str(report_data["required_headers"])+"&reportType="+str(report_data['report_type'])+"&exclude_coscholastic_subjects="+str(report_data['exclude_coscholastic_subjects'])+"&show_dimensions="+str(report_data['show_dimensions'])+"&show_total_column_dimension="+report_data["show_total_column_dimension"]+"&display_rank="+report_data["display_rank"]+"&show_coscholastic_grade="+report_data["show_coscholastic_grade"]+"&display_attendance="+report_data["display_attendance"]+"&show_scholastic_grade="+report_data["show_scholastic_grade"]+"&parent_remark="+report_data["parent_remark"]+"&user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'report' :  response.get_data()}


def get_class_exam_report(user_login_view, institute_unique_code, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/examination-reports/"+str(institute_id)+"/exam/"+report_data['exam_id']+"?section_id_str="+str(report_data['section_id_str'])+"&requiredHeaders="+str(report_data["required_headers"])+"&reportType="+str(report_data['report_type'])+"&exclude_coscholastic_subjects="+str(report_data['exclude_coscholastic_subjects'])+"&show_dimensions="+str(report_data['show_dimensions'])+"&show_total_column_dimension="+report_data["show_total_column_dimension"]+"&display_rank="+report_data["display_rank"]+"&show_coscholastic_grade="+report_data["show_coscholastic_grade"]+"&display_attendance="+report_data["display_attendance"]+"&show_scholastic_grade="+report_data["show_scholastic_grade"]+"&parent_remark="+report_data["parent_remark"]+"&user_id="+user['uuid']+"&download_format="+str(report_data['download_format']))

def get_class_course_report_data(user_login_view, institute_unique_code, standard_id, exam_id, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get(user_login_view, "/2.0/examination-reports/"+str(standard_id)+"/view-report/course/"+report_data['exam_id']+"?section_id="+str(report_data['section_id'])+"&exclude_coscholastic_subjects="+str(report_data['exclude_coscholastic_subjects'])+"&show_dimensions="+str(report_data['show_dimensions'])+"&show_total_column_dimension="+str(report_data['show_total_column_dimension'])+"&institute_id="+str(institute_id)+"&academic_session_id="+str(report_data['academicSessionId'])+"&user_id="+user['uuid']).get_data()

def get_class_course_report(user_login_view, institute_unique_code, standard_id, exam_id, report_data):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/examination-reports/"+str(standard_id)+"/course/"+report_data['exam_id']+"?section_id="+str(report_data['section_id'])+"&exclude_coscholastic_subjects="+str(report_data['exclude_coscholastic_subjects'])+"&show_dimensions="+str(report_data['show_dimensions'])+"&show_total_column_dimension="+str(report_data['show_total_column_dimension'])+"&institute_id="+str(institute_id)+"&academic_session_id="+str(report_data['academicSessionId'])+"&user_id="+user['uuid']+"&download_format="+str(report_data['download_format']))

def get_report_data(user_login_view, institute_unique_code, report_type, exam_report_filteration_criteria):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/examination-reports/"+str(institute_id)+"/view-report/"+report_type+"?user_id="+user['uuid'], exam_report_filteration_criteria)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'report' :  response.get_data()}

def get_report(user_login_view, institute_unique_code, report_type, exam_report_filteration_criteria):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.get_file_with_payload_error_reason(user_login_view, "/2.0/examination-reports/"+str(institute_id)+"/generate-report/"+report_type+"?user_id="+user['uuid'], exam_report_filteration_criteria)

def get_graphical_report(user_login_view, institute_unique_code, report_type, exam_report_filteration_criteria):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/examination-reports/"+str(institute_id)+"/view-graphical-report/"+report_type+"?user_id="+user['uuid'], exam_report_filteration_criteria)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'report' :  response.get_data()}

def clear_student_marks(user_login_view, institute_unique_code, student_id, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/examination/clear-student-marks?institute_id="+str(institute_id)+"&user_id=" + user['uuid']+"&academic_session_id=" + str(academic_session_id)+"&student_id=" + str(student_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Student Marks Cleared Successfully!!!"}

def get_personality_trait_details(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/examination/institute-personality-traits/" + str(institute_id) + "?academic_session_id="+str(academic_session_id))
    if not response.is_success():
        return None
    return response.get_data()

def bulk_add_personality_trait(user_login_view, institute_unique_code, personality_traits_detail_list, academic_session_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.post(user_login_view, "/2.0/examination/personality-traits?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid']), personality_traits_detail_list)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Personality Trait Added Successfully!!!"}

def bulk_update_personality_trait(user_login_view, institute_unique_code, personality_traits_detail_list,academic_session_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.put(user_login_view, "/2.0/examination/personality-traits/"+str(institute_id)+"?user_id="+str(user['uuid'])+"&academic_session_id="+str(academic_session_id), personality_traits_detail_list)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Personality Trait Updated Successfully!!!"}

def bulk_delete_personality_trait(user_login_view, institute_unique_code, academic_session_id, standard_id, personality_traits_str):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.delete(user_login_view, "/2.0/examination/personality-trait?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid'])+"&standard_id="+str(standard_id)+"&personality_traits_str="+personality_traits_str)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Personality Trait Deleted Successfully!!!"}

def get_personality_traits_for_standards(user_login_view, institute_unique_code, academic_session_id, standard_ids):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/examination/personality-traits/standards?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&standard_ids="+standard_ids)
    if not response.is_success():
        return []
    return response.get_data()

def bulk_delete_personality_traits_with_validation(user_login_view, institute_unique_code, academic_session_id, skip_approval, payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/examination/personality-traits/bulk?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid'])+"&skip_approval="+str(skip_approval).lower(), payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return response.get_data()

def get_standard_personality_trait_details(user_login_view, institute_unique_code, academic_session_id, standard_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/examination/standard-personality-traits/" + str(institute_id) + "?academic_session_id="+str(academic_session_id)+"&standard_id="+str(standard_id))
    if not response.is_success():
        return None
    return response.get_data()

def get_student_personality_trait_details(user_login_view, institute_unique_code, standard_id, session_id, section_id, report_type, student_id=None):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/examination/standard/" + str(standard_id) + "/personality-traits?institute_id="+str(institute_id)+"&section_id="+str(section_id)+"&academic_session_id="+str(session_id)+"&report_type="+str(report_type))
    if not response.is_success():
        return None
    return response.get_data()

def submit_personality_traits(user_login_view, institute_unique_code, report_card_variable_details, session_id, report_type):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/examination/student-personality-traits?institute_id="+str(institute_id)+"&academic_session_id="+str(session_id)+"&user_id="+user['uuid']+"&report_type="+report_type, report_card_variable_details)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    else:
        return {'success' : True , 'message' : "Data updated successfully!!!"}

def get_update_student_field_exam_data(user_login_view, institute_unique_code, academic_session_id, standard_id, section_ids, exam_id_str, additional_courses_str):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/examination/student-exams-result/{standard_id}/field-data?institute_id={institute_id}&academic_session_id={academic_session_id}&section_ids_str={section_ids}&exam_ids_str={exam_id_str}&additional_courses_str={additional_courses_str}".format(standard_id = standard_id, institute_id = str(institute_id), academic_session_id = str(academic_session_id), section_ids = str(section_ids), exam_id_str = str(exam_id_str), additional_courses_str = str(additional_courses_str)))
    if not response.is_success():
        return None
    data = response.get_data()
    if data:
        metadata = data['fieldMetadata']
        for student_data in data['studentDataList']:
            index = 0
            for field_data in student_data['fieldValueList']:
                field_data['fieldInfo'] = metadata['fieldInfoList'][index]
                index += 1
    return data

def get_student_report_card_status_data(user_login_view, institute_unique_code, academic_session_id, standard_id, section_ids, report_card_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/examination/student-report-card-status/{institute_id}?standard_id={standard_id}&academic_session_id={academic_session_id}&section_ids_str={section_ids}&report_card_id={report_card_id}".format(institute_id = str(institute_id), standard_id = standard_id, academic_session_id = str(academic_session_id), section_ids = str(section_ids), report_card_id = str(report_card_id)))
    if not response.is_success():
        return None
    return response.get_data()

def get_student_hpc_report_card_status_data(user_login_view, institute_unique_code, academic_session_id, standard_id, section_ids, exam_type):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/1.0/hpc/form/student-hpc-report-card-status/{institute_id}?standard_id={standard_id}&academic_session_id={academic_session_id}&section_ids_str={section_ids}&exam_type={exam_type}".format(institute_id = str(institute_id), standard_id = standard_id, academic_session_id = str(academic_session_id), section_ids = str(section_ids), exam_type = exam_type))
    if not response.is_success():
        return None
    return response.get_data()

def update_student_report_card_status(user_login_view, institute_unique_code, student_report_card_status_payload):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    student_report_card_status_payload['instituteId'] = institute_id
    response = restclient.post(user_login_view, "/2.0/examination/student-report-card-status?institute_id="+str(institute_id)+"&user_id="+str(user['uuid']), student_report_card_status_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Status Updated Successfully!!!"}

def update_hpc_student_report_card_status(user_login_view, institute_unique_code, academic_session_id, student_hpc_report_card_status_payload):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.post(user_login_view, "/1.0/hpc/form/student-hpc-report-card-status?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid']), student_hpc_report_card_status_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Status Updated Successfully!!!"}

### Grade Scheme
def get_grade_details(user_login_view, institute_unique_code, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/examination/grade-details/"+str(institute_id)+"?academic_session_id="+str(academic_session_id))
    if not response.is_success():
        return None
    return response.get_data()

def rename_grades(user_login_view, institute_unique_code, academic_session_id, grade_rename_payload):
    user_id = user_login_view['user']['uuid']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.put(user_login_view, "/2.0/examination/rename-grade?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user_id), grade_rename_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Grade Updated Successfully!!!"}

def add_or_update_grades(user_login_view, institute_unique_code, academic_session_id, grade_add_or_update_payload):
    user_id = user_login_view['user']['uuid']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.post(user_login_view, "/2.0/examination/bulk-grading-scheme?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user_id), grade_add_or_update_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Grade Updated Successfully!!!"}

def delete_grades(user_login_view, institute_unique_code, academic_session_id, standard_id_str):
    user_id = user_login_view['user']['uuid']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.delete(user_login_view, "/2.0/examination/grade-detail?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user_id)+"&standard_id="+str(standard_id_str))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Grade Deleted Successfully!!!"}

def download_exam_structure_document(user_login_view, institute_unique_code, academic_session_id, standard_id, exam_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file_with_error_reason(user_login_view, "/2.0/examination-reports/exam-structure-document/"+str(academic_session_id)+"/"+str(standard_id)+"?institute_id="+str(institute_id)+"&exam_id="+str(exam_id)+"&user_id="+str(user_login_view['user']['uuid']))
