{% load json %}
<div class="row mb-2 mb-xl-3">
  {% include 'core/utils/v2/academic_session_display.html'%}
      <div class="col-auto ml-auto text-right mt-n1">
        <span class="dropdown mr-2">
          <button class="btn btn-primary dropdown-toggle" id="bulk-edit-dropdown" data-placement="top" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            Bulk Edit
          </button>
          <div class="dropdown-menu" aria-labelledby="bulk-edit-dropdown">
            <a class="dropdown-item" href="#" onclick="personalityTraits.openBulkAddModal()">Bulk Add</a>
            <a class="dropdown-item" href="#" onclick="personalityTraits.openBulkDeleteModal()">Bulk Delete</a>
          </div>
        </span>
        {% include 'core/utils/v2/academic_session_dropdown.html'%}
      </div>
</div>
<div class="card">
  <div class="card-header">
  </div>
  <div id="personality-traits-list" class="card-body">
    {% include 'examination/personality_traits/personality_trait_list.html' %}
  </div>
</div>

<div id="update-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="update-personality-standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Update Personality Traits</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                    <div id="update-personality-traits-div">

                   </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="personalityTraits.updateSectionsDetails()">Update Personality Traits</button>
            </div>
        </div>
    </div>
</div>

<div id="add-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="add-personality-standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Add Personality Traits</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">

                    <div id="add-personality-traits-div">

                    </div>

                    <div>
                        <a style="text-decoration:underline;cursor:pointer;" class="mt-1" onclick="personalityTraits.addMoreClass(this);">+Add More</a>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="personalityTraits.addSectionsDetails()">Add Personality Traits</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-id-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="delete-personality-standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Delete Personality Traits</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                  <div id="delete.standard-sections-manage-id">

                  </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="personalityTraits.deleteStudentSectionsDetails()">Confirm</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="delete-personality-standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Delete Personality Traits</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                    <div id="delete-personality-traits-div">

                    </div>
                    <button type="button" class="btn btn-danger ml-4" name="button" onclick="personalityTraits.deleteSectionDetails()">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Add Modal -->
<div id="bulk-add-manage-sections-modal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Add Personality Traits</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                    <div class="form-group">
                        <label>Select Standards*</label>
                        <select class="form-control select2 bulk-standards-select mandatory-field" multiple data-toggle="select2" data-placeholder="Select Standards">
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Personality Traits*</label>
                        <div id="bulk-add-personality-traits-div">
                        </div>
                        <div>
                            <a style="text-decoration:underline;cursor:pointer;" class="mt-1" onclick="personalityTraits.addMoreClass(document.getElementById('bulk-add-personality-traits-div'));">+Add More</a>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="personalityTraits.bulkAddSectionsDetails()">Add Personality Traits</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Modal -->
<div id="bulk-delete-manage-sections-modal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Delete Personality Traits</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                    <div class="form-group">
                        <label>Select Standards*</label>
                        <select class="form-control select2 bulk-delete-standards-select mandatory-field" multiple data-toggle="select2" data-placeholder="Select Standards">
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Select Personality Traits to Delete*</label>
                        <select class="form-control select2 bulk-delete-traits-select mandatory-field" multiple data-toggle="select2" data-placeholder="Select Personality Traits">
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-danger" onclick="personalityTraits.bulkDeleteSectionsDetails()">Delete Personality Traits</button>
            </div>
        </div>
    </div>
</div>

<div id="manage-sections.status-modal-container"></div>
