from django.contrib import admin
from django.urls import path
from django.urls import include
from examination.views import *

urlpatterns = [

    # Examination Home Page
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('session-home/<academic_session_id>', home_page_session_view, name='home_page_session_view'),

    path('student-live-search/<academic_session_id>', student_live_search_view, name='student_live_search_view'),
    path('result-declaration', result_declaration_view, name='result_declaration_view'),
    path('student-exam-report-card', student_exam_report_card_view, name='student_exam_report_card_view'),

    path('class-students/<academic_session_id>', class_students_view, name='class_students_view'),
    path('class-students-with-sections/<academic_session_id>', get_student_list_view, name='get_student_list_view'),


    path('configure-exam', configure_exam_view, name='configure_exam_view'),
    path('exam-forest/<standard_id>/<academic_session_id>', get_exam_forest_view, name='get_exam_forest_view'),
    path('exam-forest-graph/<standard_id>/<academic_session_id>', get_exam_forest_graph_view, name='get_exam_forest_graph_view'),
    path('exam-details/<exam_id>', get_exam_details_view, name='get_exam_details_view'),
    path('create-exam/<isLite>', create_exam_view, name='create_exam_view'),
    # path('add-dimension', add_dimension_view, name='add_dimension_view'),
    path('update-exam-metadata/<exam_id>/<isLite>', update_exam_metadata_view, name='update_exam_metadata_view'),

    path('dimension-config', get_dimension_config_view, name='get_dimension_config_view'),
    path('dimension-list', get_dimension_list_view, name='get_dimension_list_view'),
    path('add-dimension', add_dimension_view, name='add_dimension_view'),
    path('update-dimension', update_dimension_view, name='update_dimension_view'),
    path('delete-dimension/<dimension_id>', delete_dimension_view, name='delete_dimension_view'),
    path('delete-exam/<exam_id>/<standard_id>/<isLite>', delete_exam_view, name='delete_exam_view'),

    path('datesheet-config', get_datesheet_config_view, name='get_datesheet_config_view'),
    path('datesheet-list/<academic_session_id>/<standard_id>', get_datesheet_list_view, name='get_datesheet_list_view'),
    path('view-datesheet-detail/<academic_session_id>/<standard_id>/<datesheet_id>', get_datesheet_detail_view, name='get_datesheet_detail_view'),
    path('add-datesheet', add_datesheet_view, name='add_datesheet_view'),
    path('update-datesheet', update_datesheet_view, name='update_datesheet_view'),
    path('delete-datesheet/<datesheet_id>', delete_datesheet_view, name='delete_datesheet_view'),

    path('marks-feed-input-page', marks_feed_main_page_view, name='marks_feed_main_page_view'),
    path('marks-feed-structure/<standard_id>/<academic_session_id>', marks_feed_structure_page_view, name='marks_feed_structure_page_view'),
    path('marks-feed-sheet/<academic_session_id>/<standard_id>/<exam_id>/<course_id>', marks_feed_sheet_view, name='marks_feed_sheet_view'),
    path('submit-marks/<status>/<academic_session_id>/<standard_id>', submit_marks_view, name='submit_marks_view'),
    path('update-exam-dimension/<exam_id>', update_exam_dimension_view, name='update_exam_dimension_view'),
    path('update-exam-courses/<exam_id>/<course_type>', update_exam_courses_view, name='update_exam_courses_view'),

    path('marks-view-input-page', marks_view_main_page_view, name='marks_view_main_page_view'),
    path('exam-course-structure/<standard_id>/<academic_session_id>', get_exam_course_structure_page_view, name='get_exam_course_structure_page_view'),
    path('marks-view-sheet/<academic_session_id>/<standard_id>/<exam_id>/<course_id>', marks_view_sheet_view, name='marks_view_sheet_view'),
    path('reports-only-leaf-exams/<standard_id>/<academic_session_id>', get_reports_only_leaf_exams_view, name='get_reports_only_leaf_exams_view'),
    path('report-card-types/<session_id>/<standard_id>', get_report_card_types_view, name='get_report_card_types_view'),
    path('report-card-types-name/<session_id>', get_all_report_card_types_view, name='get_all_report_card_types_view'),
    path('report-card-generation-homepage', report_card_generation_homepage_view, name='report_card_generation_homepage_view'),
    path('green-sheet', green_sheet_view, name='green_sheet_view'),
    path('generate-green-sheet/<session_id>/<standard_id>', generate_green_sheet_view, name='generate_green_sheet_view'),
    path('report-card-student-details/<session_id>/<student_id>', report_card_student_details_view, name='report_card_student_details_view'),
    path('student-report-card/<session_id>/<student_id>/<report_type>', student_report_card_view, name='student_report_card_view'),
    path('class-report-card/<session_id>/<standard_id>/<report_type>', class_report_card_view, name='class_report_card_view'),
    path('bulk-student-report-card/<session_id>/<student_ids>/<standard_id>/<report_type>', bulk_students_report_card_view, name='bulk_students_report_card_view'),

    path('load-bulk-page',load_bulk_page_view,name = 'load_bulk_page_view'),


    path('report-card-variables-input-view', report_card_variables_view, name='report_card_variables_view'),
    path('view-report-card-variables/<standard_id>/<session_id>', view_report_card_variables_view, name='view_report_card_variables_view'),
    path('view-personality-trait/<standard_id>/<session_id>', view_personality_trait_view, name='view_personality_trait_view'),
    path('submit-report-card-variables', submit_report_card_variables_view, name='submit_report_card_variables_view'),
    path('report-card-variable-types/<session_id>/<standard_id>', get_report_card_variable_types_view, name='get_report_card_variable_types_view'),
    path('report-card-variables/session-details/<session_id>', report_card_variables_with_session_details_view, name='report_card_variables_with_session_details_view'),
    path('student-report-variable-pdf/<session_id>/<student_id>/<report_type>', student_report_variable_pdf_view, name='student_report_card_view'),
    path('report-card-variable-page/<session_id>/<student_id>/<report_type>/<standard_id>', student_report_variable_page_view, name='student_report_variable_page_view'),

    path('reports', reports_view, name='reports_view'),
    path('generate-report/<exam_id>', generate_report_view, name='generate_report_view'),
    path('generate-course-report/<standard_id>/<exam_id>', generate_course_report_view, name='generate_course_report_view'),
    path('exam-course-details/<standard_id>/<academic_session_id>', get_exam_course_details_view, name='get_exam_course_details_view'),
    path('section-details/<standard_id>/<academic_session_id>', get_section_details_view, name='get_section_details_view'),
    path('generate-reports/<report_type>', generate_reports_view, name='generate_reports_view'),
    path('generate-graphical-reports/<report_type>', generate_graphical_reports_view, name='generate_graphical_reports_view'),

    path('admitcard', admitcard_view, name='admitcard_view'),
    path('admitcard-session-change/<academic_session_id>', admitcard_session_change_view, name='admitcard_session_change_view'),
    path('generate-admitcard/<exam_id>/<admit_card_type>', generate_admitcard_view, name='generate_admitcard_view'),

    path('generate-feed-marks-sheet/<academic_session_id>/<standard_id>/<exam_id>', generate_feed_marks_sheet_view, name='generate_feed_marks_sheet_view'),

    path('standards/<academic_session_id>', get_session_standards_view, name='get_session_standards_view'),
    path('standards-json/<academic_session_id>', get_session_standards_json_view, name='get_session_standards_json_view'),
    path('get-standards-json/<academic_session_id>', get_standards_json_view, name='get_standards_json_view'),

    path('get-channel-credits',  get_channel_credits_view, name='get_channel_credits_view'),
    path('notification-input-page', notification_main_page_view, name='notification_main_page_view'),
    path('exam-notification-detail/<standard_id>/<academic_session_id>', get_exam_notification_detail_view, name='get_exam_notification_detail_view'),
    path('student-details-sheet/<academic_session_id>/<standard_id>/<exam_id>/<course_id>', student_details_sheet_view, name='student_details_sheet_view'),
    path('notification-templates', bulk_notification_template_selection_view, name='bulk_notification_template_selection_view'),
    path('reminder-templates', bulk_reminder_template_selection_view, name='bulk_reminder_template_selection_view'),
    path('send-notifications/<send_to_absentees>/<exam_id>/<course_id>', send_notifications_view, name='send_notifications_view'),

    path('notification-history-homepage', notification_history_homepage_view, name='notification_history_homepage_view'),
    path('notification-history/<academic_session_id>/<delivery_mode>/<notificationHistoryTab>/<offset>/<limit>', notification_history_view, name='notification_history_view'),
    path('batch-notification-details/<batch_id>', batch_notification_details_view, name='batch_notification_details_view'),

    path('advance-marks-feed-input-page', advance_marks_feed_main_page_view, name='advance_marks_feed_main_page_view'),
    path('advance-marks-feed-sheet/<exam_id>/<standard_id>/<academic_session_id>', advance_marks_feed_sheet_view, name='advance_marks_feed_sheet_view'),

    path('pdf-datesheet/<datesheet_id>/<standard_id>', datesheet_pdf_view, name='datesheet_pdf_view'),

    path('publish-exam-config', get_publish_exam_config_view, name='get_publish_exam_config_view'),
    path('publish-exam-list/<academic_session_id>/<standard_id>', get_publish_exam_list_view, name='get_publish_exam_list_view'),

    path('update-exam-status/<exam_id>/<status>', update_exam_status_view, name='update_exam_status_view'),
    path('student-list/<academic_session_id>/<standard_id>', get_student_list_view, name='get_student_list_view'),

    path('courses-marks-status-details/<academic_session_id>/<exam_id>', courses_marks_status_details_view, name='courses_marks_status_details_view'),
    path('update-course-exam-status/<dimension_id>/<course_id>/<exam_id>/<status>/<academic_session_id>/<standard_id>', update_course_exam_status_view, name='update_course_exam_status_view'),


    path('personality-traits-home', personality_traits_home_view, name='personality_traits_home_view'),
    path('personality-traits/<academic_session_id>', personality_traits_view, name='personality_traits_view'),
    path('personality-traits-standards', get_personality_traits_for_standards_view, name='get_personality_traits_for_standards_view'),
    path('personality-traits-bulk-delete', bulk_delete_personality_traits_with_validation_view, name='bulk_delete_personality_traits_with_validation_view'),
    path('bulk-update-personality-trait/<academic_session_id>', bulk_update_personality_trait_view, name='bulk_update_personality_trait_view'),
    path('bulk-add-personality-trait/<academic_session_id>', bulk_add_personality_trait_view, name='bulk_add_personality_trait_view'),
    path('bulk-delete-personality-trait/<academic_session_id>/<standard_id>/<personality_traits_str>', bulk_delete_personality_trait_view, name='bulk_delete_personality_trait_view'),
    path('submit-personality-traits/<session_id>/<report_type>', submit_personality_traits_view, name='submit_personality_traits_view'),

    ## Exam Update Student Info
    path('student-details-home-v2', student_details_home_v2_view, name='student_details_home_v2_view'),
    path('update-page-session-change-v2/<academic_session_id>', update_page_session_change_v2_view, name='update_page_session_change_v2_view'),
    path('student-list-v2/<academic_session_id>/<standard_id>', update_student_list_v2_view, name='update_student_list_v2_view'),
    path('update-student-details-v2/<academic_session_id>/<standard_id>', update_student_details_v2_view, name='update_student_details_v2_view'),
    ###

    ## Exam Update Student Info
    path('publish-report-card-home', publish_report_card_home_view, name='publish_report_card_home_view'),
    path('publish-report-card-session-home/<academic_session_id>', publish_report_card_session_home_view, name='publish_report_card_session_home_view'),
    path('report-card-variable-types-with-id/<session_id>/<standard_id>', get_report_card_variable_types_with_id_view, name='get_report_card_variable_types_with_id_view'),
    path('student-report-card-status/<academic_session_id>/<standard_id>/<report_card_id>', student_report_card_status_view, name='student_report_card_status_view'),
    path('update-student-report-card-status', update_student_report_card_status_view, name='update_student_report_card_status_view'),
    ###

    ## Exam Grade Scheme
    path('exam-grade-details', exam_grade_details_view, name='exam_grade_details_view'),
    path('exam-grade-details-with-session/<session_id>', exam_grade_details_with_session_view, name='exam_grade_details_with_session_view'),
    path('rename-grades/<academic_session_id>', rename_grades_view, name='rename_grades_view'),
    path('add-update-grades/<academic_session_id>', add_update_grades_view, name='add_update_grades_view'),
    path('delete-grades/<academic_session_id>', delete_grades_view, name='delete_grades_view'),
    ##

    # HPC

    path('hpc/form/homepage', hpc_form_homepage_view, name='hpc_form_homepage_view'),
    path('hpc/student-form/<academic_session_id>/<student_id>/<exam_type>', student_hpc_form_view, name='student_hpc_form_view'),
    path('hpc/form-data', hpc_form_data_view, name='hpc_form_data_view'),
    path('hpc/class-students-list/<academic_session_id>', class_students_list_view, name='class_students_list_view'),
    path('hpc/delete-document/<academic_session_id>/<exam_type>/<hpc_user_type>/<student_id>/<id>', delete_hpc_document_view, name='delete_hpc_document_view'),
    path('hpc/download-document/<academic_session_id>/<exam_type>/<hpc_user_type>/<student_id>/<id>', download_hpc_document_view, name='download_hpc_document_view'),
    path('hpc-report-card-generation-homepage', hpc_report_card_generation_homepage_view, name='hpc_report_card_generation_homepage_view'),
    path('hpc-report-card-student-details/<session_id>/<student_id>', hpc_report_card_student_details_view, name='hpc_report_card_student_details_view'),
    path('student-hpc-report-card/<session_id>/<student_id>/<exam_type>', student_hpc_report_card_view, name='student_hpc_report_card_view'),
    path('class-hpc-report-card/<session_id>/<standard_id>/<exam_type>', class_hpc_report_card_view, name='class_hpc_report_card_view'),
    path('bulk-student-hpc-report-card/<session_id>/<student_ids>/<standard_id>/<exam_type>', bulk_students_hpc_report_card_view, name='bulk_students_hpc_report_card_view'),
    path('hpc-class-students/<academic_session_id>', hpc_class_students_view, name='hpc_class_students_view'),

    ## HPC REPORT CARD
    path('publish-hpc-report-card-home', publish_hpc_report_card_home_view, name='publish_hpc_report_card_home_view'),
    path('publish-hpc-report-card-session-home/<academic_session_id>', publish_hpc_report_card_session_home_view, name='publish_hpc_report_card_session_home_view'),
    path('student-hpc-report-card-status/<academic_session_id>/<standard_id>/<exam_card_type>', student_hpc_report_card_status_view, name='student_hpc_report_card_status_view'),
    path('update-hpc-student-report-card-status', update_hpc_student_report_card_status_view, name='update_hpc_student_report_card_status_view'),
    ###

    path('download-exam-structure-document/<academic_session_id>/<standard_id>', download_exam_structure_document_view, name='download_exam_structure_document_view'),

]
